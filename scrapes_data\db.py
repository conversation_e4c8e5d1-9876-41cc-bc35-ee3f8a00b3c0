import mysql.connector

def get_connection():
    """Connect to MySQL database."""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="1234",
            database="vnex_scrape"
        )
    except mysql.connector.Error as e:
        print(f"Database error: {e}")
        return None

def save_to_db(title, description):
    """Save article to MySQL if not duplicate."""
    conn = get_connection()
    if not conn:
        return
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM articles_scrape WHERE title = %s", (title,))
        if cursor.fetchone()[0] > 0:
            print(f"Article '{title}' already exists, skipping.")
            return
        cursor.execute("INSERT INTO articles_scrape (title, description) VALUES (%s, %s)",
                       (title, description))
        conn.commit()
        print("Saved to MySQL!")
    except mysql.connector.Error as e:
        print(f"Error saving: {e}")
    finally:
        conn.close()

def get_existing_titles():
    """Get all existing titles from database."""
    conn = get_connection()
    if not conn:
        return []
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT title FROM articles_scrape")
        return [row[0] for row in cursor.fetchall()]
    except mysql.connector.Error as e:
        print(f"Error fetching titles: {e}")
        return []
    finally:
        conn.close()


def update_article(title, description):
    """Update existing article in MySQL."""
    conn = get_connection()
    if not conn:
        return
    try:
        cursor = conn.cursor()
        cursor.execute("UPDATE articles_scrape SET description = %s WHERE title = %s",
                      (description, title))
        conn.commit()
        print(f"Updated article: {title}")
    except mysql.connector.Error as e:
        print(f"Error updating: {e}")
    finally:
        conn.close()
