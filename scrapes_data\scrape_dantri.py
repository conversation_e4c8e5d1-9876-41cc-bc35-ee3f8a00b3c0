import asyncio
import aiohttp
from bs4 import BeautifulSoup
import os
import random
from config import BASE_URL, TIMEOUT, CHECKPOINT_FILE, NEW_ARTICLES_FILE, DELAY
from db import save_to_db, get_existing_titles, update_article

# List of user-agents to rotate
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:91.0) Gecko/20100101 Firefox/91.0",
]

async def fetch_url(session, url):
    """Fetch HTML content with headers, retry logic, and random user-agent.
    
    Args:
        session (aiohttp.ClientSession): The session to use for requests.
        url (str): The URL to fetch.
    
    Returns:
        str: HTML content or None if failed.
    """
    headers = {"User-Agent": random.choice(USER_AGENTS)}
    for attempt in range(3):
        try:
            async with session.get(url, timeout=TIMEOUT, headers=headers) as response:
                response.raise_for_status()
                return await response.text()
        except aiohttp.ClientResponseError as e:
            if e.status == 429:
                print(f"429 Too Many Requests for {url} (attempt {attempt + 1}/3): {e}")
                await asyncio.sleep(2 ** attempt + DELAY)
            else:
                print(f"Fetch error for {url} (attempt {attempt + 1}/3): {e}")
            if attempt == 2:
                return None
            await asyncio.sleep(2 ** attempt)

def log_new_article(title, url):
    """Log new article to a file.
    
    Args:
        title (str): Article title.
        url (str): Article URL.
    """
    with open(NEW_ARTICLES_FILE, "a", encoding="utf-8") as f:
        f.write(f"Title: {title}\nURL: {url}\n---\n")

async def parse_article(session, url, existing_titles):
    await asyncio.sleep(random.uniform(0.5, 2.0))  # Thêm delay ngẫu nhiên
    html = await fetch_url(session, url)
    if not html:
        return
    soup = BeautifulSoup(html, "html.parser")
    title_tag = soup.find("h1", class_="title-page detail")
    desc_tag = soup.find("p", class_="sapo")
    title = title_tag.get_text(strip=True) if title_tag else "No title"
    desc = desc_tag.get_text(strip=True) if desc_tag else "No description"
    if title in existing_titles:
        current_desc = next((row[1] for row in get_existing_titles() if row[0] == title), "")
        if desc != current_desc:  # Check if content changed
            update_article(title, desc)
            print(f"> Updated: {title}\n> New Desc: {desc}\n> URL: {url}\n---")
    else:
        print(f"> New Title: {title}\n> Desc: {desc}\n> URL: {url}\n---")
        save_to_db(title, desc)
        existing_titles.append(title)
        log_new_article(title, url)

async def scrape_page(session, page_url, existing_titles, sem):
    async with sem:
        html = await fetch_url(session, page_url)
        if not html:
            return False
        soup = BeautifulSoup(html, "html.parser")
        articles = soup.find_all("h3", class_="article-title")  # Cập nhật lớp
        if not articles:
            print(f"! No posts found on {page_url}")
            return False
        print(f"Found {len(articles)} posts on {page_url}")
        tasks = []
        for article in articles:
            a_tag = article.find("a")
            if a_tag and (href := a_tag.get("href")):
                article_url = f"https://dantri.com.vn{href}" if href.startswith("/") else href
                tasks.append(parse_article(session, article_url, existing_titles))
        await asyncio.gather(*tasks)
        return True

# Trong hàm main
async def main():
    start_page = 1 if not os.path.exists(CHECKPOINT_FILE) else int(open(CHECKPOINT_FILE, "r").read().strip())
    print(f"Resuming from page {start_page}")

    existing_titles = get_existing_titles()
    print(f"Found {len(existing_titles)} existing articles")

    async with aiohttp.ClientSession() as session:
        sem = asyncio.Semaphore(2)  # Giảm số lượng tác vụ đồng thời
        page = start_page
        while True:
            if page == 1:
                page_url = BASE_URL
            else:
                # Lấy phần chuyên mục, bỏ .htm ở cuối
                if BASE_URL.endswith('.htm'):
                    category_url = BASE_URL[:-4]
                else:
                    category_url = BASE_URL
                page_url = f"{category_url}/trang-{page}.htm"
            print(f"Scraping page {page}")
            with open(CHECKPOINT_FILE, "w") as f:
                f.write(str(page))
            if not await scrape_page(session, page_url, existing_titles, sem):
                break
            page += 1
            await asyncio.sleep(DELAY)

    if os.path.exists(CHECKPOINT_FILE):
        os.remove(CHECKPOINT_FILE)
        print("Checkpoint deleted. Scrape completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Scraping interrupted. Checkpoint saved.")
    except Exception as e:
        print(f"Error: {e}")
