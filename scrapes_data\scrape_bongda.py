import asyncio
import aiohttp
from bs4 import BeautifulSoup
import os
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from config import BASE_URL, TIMEOUT, MAX_THREADS, CHECKPOINT_FILE
from db import save_to_db, get_existing_titles


async def fetch(session, url, max_retries=3):
    for attempt in range(max_retries):
        try:
            async with session.get(url, timeout=TIMEOUT) as response:
                response.raise_for_status()
                return await response.text()
        except Exception as e:
            print(f"Fetch error for {url} (attempt {attempt+1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                return None
            await asyncio.sleep(2 ** attempt)


async def scrape_page(page_url, existing_titles):
    async with aiohttp.ClientSession() as session:
        html = await fetch(session, page_url)
        if not html:
            return False

        soup = BeautifulSoup(html, "html.parser")
        articles = soup.select("article")

        if not articles:
            print(f"! No articles found on {page_url}")
            return False

        print(f"Found {len(articles)} articles on {page_url}")
        for article in articles:
            a_tag = article.select_one("h3.box-title-text")
            desc_tag = article.select_one("p.box-category-sapo")

            if not a_tag:
                continue

            title = a_tag.get_text(strip=True)
            href = a_tag.get("href")
            description = desc_tag.get_text(strip=True) if desc_tag else "No description"
            full_url = f"https://tuoitre.vn{href}" if href.startswith("/") else href

            if title not in existing_titles:
                print(f"Title: {title}")
                print(f"Description: {description}")
                print(f"URL: {full_url}")
                print("---")
                save_to_db(title, description)

        return True


def scrape_page_thread(page_url, existing_titles):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(scrape_page(page_url, existing_titles))
    finally:
        loop.close()


if __name__ == "__main__":
    start_page = 1
    if os.path.exists(CHECKPOINT_FILE):
        with open(CHECKPOINT_FILE, "r") as f:
            start_page = int(f.read().strip())
        print(f"Resuming from page {start_page}")

    existing_titles = get_existing_titles()
    print(f"Found {len(existing_titles)} existing articles in database.")
    print("Starting scrape")

    with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
        page = start_page
        continue_scraping = True

        while continue_scraping:
            print(f"Scraping page {page}")
            futures = []
            for _ in range(MAX_THREADS):
                page_url = BASE_URL if page == 1 else BASE_URL.replace("", f"/trang{page}.htm")
                futures.append(executor.submit(scrape_page_thread, page_url, existing_titles))

                with open(CHECKPOINT_FILE, "w") as f:
                    f.write(str(page))
                page += 1

            continue_scraping = False
            for future in futures:
                try:
                    if future.result(timeout=TIMEOUT):
                        continue_scraping = True
                except Exception as e:
                    print(f"Thread error: {e}")

            if not continue_scraping:
                print("No more articles found. Stopping scrape.")

    if os.path.exists(CHECKPOINT_FILE):
        os.remove(CHECKPOINT_FILE)
        print("Checkpoint deleted. Scrape completed!")
